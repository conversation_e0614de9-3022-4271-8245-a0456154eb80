import { Link } from "@heroui/link";
import { Button } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { Spacer } from "@heroui/spacer";

import { siteConfig } from "@/config/site";
import { title, subtitle } from "@/components/primitives";
import {
  HtmlIcon,
  PdfIcon,
  ResumeIcon,
  CloudIcon,
  SpeedIcon,
  SecurityIcon,
  GlobalIcon,
  RocketIcon
} from "@/components/icons";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 py-16 md:py-24 text-center">
        <div className="max-w-4xl mx-auto px-6">
          <h1 className={title({ size: "lg" })}>
            Lightning-fast&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              static hosting&nbsp;
            </span>
            <br />
            for modern web applications
          </h1>
          <div className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
            Deploy HTML sites, PDFs, resumes, and documents instantly with zero configuration.
            Global CDN, SSL certificates, and custom domains included.
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <Button
            size="lg"
            color="primary"
            variant="shadow"
            className="font-semibold px-8 py-6 text-lg"
            style={{ backgroundColor: "#b249f8" }}
          >
            <RocketIcon size={20} />
            Deploy Now - Free
          </Button>
          <Button
            size="lg"
            variant="bordered"
            className="font-semibold px-8 py-6 text-lg"
          >
            View Documentation
          </Button>
        </div>

        <div className="mt-12 text-sm text-default-500">
          ⚡ Deploy in seconds • 🌍 Global CDN • 🔒 Free SSL • 📱 Mobile optimized
        </div>
      </section>

      <Spacer y={8} />

      {/* Services Section - Bento Grid */}
      <section className="bg-[#0a0a0f] py-24 sm:py-32 relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#b249f8]/5 via-transparent to-blue-500/5"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-[#b249f8]/10 via-transparent to-transparent"></div>

        <div className="mx-auto max-w-2xl px-6 lg:max-w-7xl lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-center text-sm font-medium text-[#b249f8] uppercase tracking-wider mb-4">Deploy faster</h2>
            <p className="mx-auto mt-2 max-w-4xl text-center text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              Perfect hosting for every project
            </p>
            <p className="mx-auto mt-6 max-w-2xl text-center text-lg text-gray-400 leading-relaxed">
              From simple HTML pages to complex documents, we've got you covered with specialized hosting solutions.
            </p>
          </div>

          {/* Bento Grid Layout */}
          <div className="mt-16 grid gap-4 lg:grid-cols-12 lg:grid-rows-8 h-[800px]">

            {/* HTML Hosting - Large Featured Card */}
            <div className="lg:col-span-7 lg:row-span-4 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 overflow-hidden hover:border-[#b249f8]/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#b249f8]/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 right-0 w-64 h-64 bg-[#b249f8]/10 rounded-full blur-3xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-6 mb-8">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-[#b249f8] to-purple-600 shadow-lg shadow-[#b249f8]/25 float-animation">
                      <HtmlIcon size={32} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-white mb-3">HTML Hosting</h3>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className="px-3 py-1.5 bg-[#b249f8]/20 text-[#b249f8] text-sm rounded-full font-medium border border-[#b249f8]/30">Most Popular</span>
                        <span className="px-3 py-1.5 bg-emerald-500/20 text-emerald-400 text-sm rounded-full font-medium border border-emerald-500/30">Lightning Fast</span>
                      </div>
                      <p className="text-gray-300 leading-relaxed">
                        Deploy static websites, SPAs, and HTML applications with lightning speed. Perfect for portfolios, landing pages, and modern web applications.
                      </p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="grid grid-cols-2 gap-4 mb-8">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                      <span className="text-sm text-gray-400">Global CDN</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                      <span className="text-sm text-gray-400">Auto SSL</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                      <span className="text-sm text-gray-400">Git Integration</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                      <span className="text-sm text-gray-400">Custom Domains</span>
                    </div>
                  </div>

                  {/* Mock Browser Window */}
                  <div className="flex-1 bg-gray-800/50 rounded-xl border border-gray-700/50 overflow-hidden mb-6">
                    <div className="flex items-center gap-2 bg-gray-900/80 px-4 py-3 border-b border-gray-700/50">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div className="flex-1 bg-gray-800 rounded px-3 py-1.5 text-xs text-gray-400 ml-4">
                        https://your-site.statichost.com
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div className="h-4 bg-gradient-to-r from-[#b249f8] to-purple-500 rounded-full w-3/4"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-1/2"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-2/3"></div>
                      <div className="h-3 bg-gray-600 rounded-full w-1/3"></div>
                      <div className="grid grid-cols-3 gap-3 mt-6">
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                        <div className="h-16 bg-gray-700 rounded-lg"></div>
                      </div>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="flex gap-3">
                    <Button
                      color="primary"
                      variant="shadow"
                      className="flex-1 bg-[#b249f8] hover:bg-[#a142e4] text-white font-semibold"
                    >
                      Deploy Now
                    </Button>
                    <Button
                      variant="bordered"
                      className="flex-1 border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white"
                    >
                      View Demo
                    </Button>
                  </div>
                </div>
              </div>
            </div>



            {/* PDF Hosting - Medium Card */}
            <div className="lg:col-span-3 lg:row-span-2 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-orange-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute bottom-0 right-0 w-32 h-32 bg-orange-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-600 shadow-lg shadow-orange-500/25 float-animation">
                      <PdfIcon size={20} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white mb-2">PDF Hosting</h3>
                      <span className="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs rounded-full font-medium border border-orange-500/30">Secure</span>
                      <p className="text-gray-300 text-sm leading-relaxed mt-3">
                        Share documents, manuals, and reports with secure, fast PDF hosting.
                      </p>
                    </div>
                  </div>

                  {/* PDF Preview Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden mb-4">
                    <div className="flex items-center gap-3 bg-gray-900/80 px-3 py-2 border-b border-gray-700/50">
                      <PdfIcon size={12} className="text-orange-400" />
                      <span className="text-xs font-medium text-gray-300">annual-report.pdf</span>
                      <div className="ml-auto flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-500">Protected</span>
                      </div>
                    </div>
                    <div className="p-3 space-y-2">
                      <div className="h-1.5 bg-gradient-to-r from-orange-400 to-red-400 rounded-full w-full"></div>
                      <div className="h-1 bg-gray-600 rounded-full w-4/5"></div>
                      <div className="h-1 bg-gray-600 rounded-full w-3/5"></div>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2 text-xs mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Password protection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Download restrictions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      <span className="text-gray-400">Analytics tracking</span>
                    </div>
                  </div>

                  <Button
                    color="warning"
                    variant="flat"
                    className="w-full bg-orange-500/20 text-orange-400 border-orange-500/30 hover:bg-orange-500/30 text-sm"
                    size="sm"
                  >
                    Learn More
                  </Button>
                </div>
              </div>
            </div>

            {/* Resume Hosting - Medium Card */}
            <div className="lg:col-span-3 lg:row-span-2 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-green-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 left-0 w-32 h-32 bg-green-500/10 rounded-full blur-2xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg shadow-green-500/25 float-animation">
                      <ResumeIcon size={20} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white mb-2">Resume Hosting</h3>
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full font-medium border border-green-500/30">Professional</span>
                      <p className="text-gray-300 text-sm leading-relaxed mt-3">
                        Professional resume hosting with custom URLs. Perfect for job applications.
                      </p>
                    </div>
                  </div>

                  {/* Resume Preview Mock */}
                  <div className="flex-1 bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden mb-4">
                    <div className="p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">JD</span>
                        </div>
                        <div className="flex-1">
                          <div className="h-2 bg-gray-600 rounded w-16 mb-1"></div>
                          <div className="h-1.5 bg-gray-700 rounded w-12"></div>
                        </div>
                      </div>
                      <div className="space-y-1.5 mb-3">
                        <div className="h-1.5 bg-gray-600 rounded-full w-full"></div>
                        <div className="h-1.5 bg-gray-600 rounded-full w-3/4"></div>
                        <div className="h-1.5 bg-gray-600 rounded-full w-1/2"></div>
                      </div>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2 text-xs mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                      <span className="text-gray-400">Custom domain</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                      <span className="text-gray-400">SEO optimized</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                      <span className="text-gray-400">View analytics</span>
                    </div>
                  </div>

                  <Button
                    color="success"
                    variant="flat"
                    className="w-full bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30 text-sm"
                    size="sm"
                  >
                    Get Started
                  </Button>
                </div>
              </div>
            </div>

            {/* Advanced Features - Coming Soon */}
            <div className="lg:col-span-6 lg:row-span-2 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-purple-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-0 right-0 w-48 h-48 bg-purple-500/10 rounded-full blur-3xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-4 mb-6">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg shadow-purple-500/25 float-animation">
                      <CloudIcon size={24} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-2">Advanced Features</h3>
                      <span className="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full font-medium border border-purple-500/30">Coming Soon</span>
                      <p className="text-gray-300 text-sm leading-relaxed mt-3">
                        Database hosting, API endpoints, serverless functions, and more advanced features coming soon.
                      </p>
                    </div>
                  </div>

                  {/* Code Editor Mock */}
                  <div className="flex-1 bg-gray-900/80 rounded-lg border border-gray-700/50 overflow-hidden mb-4">
                    <div className="flex bg-gray-800/60 border-b border-gray-700/50">
                      <div className="flex text-xs font-medium text-gray-400">
                        <div className="border-r border-gray-700/50 bg-gray-700/30 px-3 py-2 text-white">
                          api.js
                        </div>
                        <div className="px-3 py-2">database.js</div>
                      </div>
                    </div>
                    <div className="p-4 space-y-2 text-xs">
                      <div className="flex items-center gap-2">
                        <span className="text-purple-400">export</span>
                        <span className="text-blue-400">async function</span>
                        <span className="text-yellow-300">handler</span>
                        <span className="text-gray-300">(req, res) &#123;</span>
                      </div>
                      <div className="ml-3 space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-blue-400">const</span>
                          <span className="text-white">data</span>
                          <span className="text-gray-300">=</span>
                          <span className="text-blue-400">await</span>
                          <span className="text-yellow-300">db.query</span>
                          <span className="text-gray-300">();</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-white">res</span>
                          <span className="text-gray-300">.</span>
                          <span className="text-yellow-300">json</span>
                          <span className="text-gray-300">(data);</span>
                        </div>
                      </div>
                      <div className="text-gray-300">&#125;</div>
                    </div>
                  </div>

                  {/* Feature list */}
                  <div className="grid grid-cols-2 gap-3 text-xs mb-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                        <span className="text-gray-400">Database hosting</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                        <span className="text-gray-400">API endpoints</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                        <span className="text-gray-400">Serverless functions</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
                        <span className="text-gray-400">Edge computing</span>
                      </div>
                    </div>
                  </div>

                  <Button
                    color="secondary"
                    variant="flat"
                    size="sm"
                    disabled
                    className="w-full bg-purple-500/20 text-purple-400 border-purple-500/30 opacity-60 text-sm"
                  >
                    Notify Me
                  </Button>
                </div>
              </div>
            </div>

            {/* Performance Stats Card */}
            <div className="lg:col-span-6 lg:row-span-2 group">
              <div className="relative h-full bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 overflow-hidden hover:border-cyan-500/50 transition-all duration-500">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-cyan-500/10 rounded-full blur-3xl opacity-20"></div>

                <div className="relative z-10 h-full flex flex-col">
                  {/* Header */}
                  <div className="flex items-start gap-4 mb-6">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 shadow-lg shadow-cyan-500/25">
                      <SpeedIcon size={24} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-2">Performance Stats</h3>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Lightning-fast global performance with enterprise-grade reliability and monitoring.
                      </p>
                    </div>
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                      <div className="text-2xl font-bold text-cyan-400 mb-1">99.9%</div>
                      <div className="text-xs text-gray-400">Uptime</div>
                    </div>
                    <div className="text-center bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                      <div className="text-2xl font-bold text-blue-400 mb-1">&lt;50ms</div>
                      <div className="text-xs text-gray-400">Response</div>
                    </div>
                    <div className="text-center bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                      <div className="text-2xl font-bold text-indigo-400 mb-1">200+</div>
                      <div className="text-xs text-gray-400">Locations</div>
                    </div>
                    <div className="text-center bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                      <div className="text-2xl font-bold text-purple-400 mb-1">10M+</div>
                      <div className="text-xs text-gray-400">Sites</div>
                    </div>
                  </div>

                  {/* Performance Bar */}
                  <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                    <div className="flex items-center justify-between text-xs mb-2">
                      <span className="text-gray-400">Global Performance</span>
                      <span className="text-green-400 font-medium">Excellent</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-gradient-to-r from-cyan-500 to-blue-600 h-2 rounded-full w-[95%]"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Features Section */}
      <section className="py-24 px-6 bg-[#0a0a0f] relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#b249f8]/5 via-transparent to-blue-500/5"></div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why choose&nbsp;
              <span className="text-[#b249f8]">
                StaticHost?
              </span>
            </h2>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Built for speed, security, and simplicity. Everything you need to get your content online fast.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-[#b249f8]/20 to-purple-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-[#b249f8]/30 group-hover:border-[#b249f8]/50 transition-all duration-300">
                <SpeedIcon size={32} className="text-[#b249f8]" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Lightning Fast</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Global CDN ensures your content loads instantly worldwide
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-green-500/20 to-emerald-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-green-500/30 group-hover:border-green-500/50 transition-all duration-300">
                <SecurityIcon size={32} className="text-green-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Secure by Default</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Free SSL certificates and DDoS protection included
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500/20 to-cyan-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-blue-500/30 group-hover:border-blue-500/50 transition-all duration-300">
                <GlobalIcon size={32} className="text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Global Reach</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Deployed across 200+ edge locations worldwide
              </p>
            </div>

            <div className="text-center group">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-orange-500/20 to-yellow-600/20 w-16 h-16 mx-auto mb-6 flex items-center justify-center border border-orange-500/30 group-hover:border-orange-500/50 transition-all duration-300">
                <CloudIcon size={32} className="text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold mb-3 text-white">Zero Config</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Deploy with a simple drag & drop or Git integration
              </p>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* CTA Section */}
      <section className="py-24 px-6 bg-[#0a0a0f] relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#b249f8]/10 via-transparent to-blue-500/10"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-[#b249f8]/20 rounded-full blur-3xl opacity-30"></div>

        <div className="max-w-4xl mx-auto text-center relative z-10">
          <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-12 shadow-2xl">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to deploy your&nbsp;
              <span className="text-[#b249f8]">
                next project?
              </span>
            </h2>
            <p className="text-lg text-gray-400 mt-4 mb-8 max-w-2xl mx-auto leading-relaxed">
              Join thousands of developers who trust StaticHost for their hosting needs.
              Start with our free tier and scale as you grow.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button
                size="lg"
                color="primary"
                variant="shadow"
                className="font-semibold px-8 py-6 text-lg bg-[#b249f8] hover:bg-[#a142e4] text-white shadow-lg shadow-[#b249f8]/25"
              >
                <RocketIcon size={20} />
                Start Free Trial
              </Button>
              <Button
                size="lg"
                variant="bordered"
                className="font-semibold px-8 py-6 text-lg border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white"
                as={Link}
                href="/pricing"
              >
                View Pricing
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                No credit card required
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Free SSL & CDN
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                24/7 Support
              </div>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Stats Section */}
      <section className="py-24 px-6 bg-[#0a0a0f] border-t border-gray-800/50">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-[#b249f8] mb-2 group-hover:scale-110 transition-transform duration-300">99.9%</div>
              <div className="text-gray-400">Uptime SLA</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2 group-hover:scale-110 transition-transform duration-300">200+</div>
              <div className="text-gray-400">Edge Locations</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300">10M+</div>
              <div className="text-gray-400">Sites Hosted</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2 group-hover:scale-110 transition-transform duration-300">&lt;100ms</div>
              <div className="text-gray-400">Average Response</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
