import { Link } from "@heroui/link";
import { <PERSON><PERSON> } from "@heroui/button";
import { <PERSON>, <PERSON><PERSON>eader, CardB<PERSON>, CardFooter } from "@heroui/card";
import { Spacer } from "@heroui/spacer";
import { Image } from "@heroui/image";

import { siteConfig } from "@/config/site";
import { title, subtitle } from "@/components/primitives";
import {
  HtmlIcon,
  PdfIcon,
  ResumeIcon,
  CloudIcon,
  SpeedIcon,
  SecurityIcon,
  GlobalIcon,
  RocketIcon
} from "@/components/icons";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 py-16 md:py-24 text-center">
        <div className="max-w-4xl mx-auto px-6">
          <h1 className={title({ size: "lg" })}>
            Lightning-fast&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              static hosting&nbsp;
            </span>
            <br />
            for modern web applications
          </h1>
          <div className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
            Deploy HTML sites, PDFs, resumes, and documents instantly with zero configuration.
            Global CDN, SSL certificates, and custom domains included.
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <Button
            size="lg"
            color="primary"
            variant="shadow"
            className="font-semibold px-8 py-6 text-lg"
            style={{ backgroundColor: "#b249f8" }}
          >
            <RocketIcon size={20} />
            Deploy Now - Free
          </Button>
          <Button
            size="lg"
            variant="bordered"
            className="font-semibold px-8 py-6 text-lg"
          >
            View Documentation
          </Button>
        </div>

        <div className="mt-12 text-sm text-default-500">
          ⚡ Deploy in seconds • 🌍 Global CDN • 🔒 Free SSL • 📱 Mobile optimized
        </div>
      </section>

      <Spacer y={8} />

      {/* Services Section - Bento Grid */}
      <section className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-2xl px-6 lg:max-w-7xl lg:px-8">
          <h2 className="text-center text-base/7 font-semibold text-[#b249f8]">Deploy faster</h2>
          <p className="mx-auto mt-2 max-w-lg text-center text-4xl font-semibold tracking-tight text-balance text-gray-950 sm:text-5xl">
            Perfect hosting for every project
          </p>
          <p className="mx-auto mt-4 max-w-2xl text-center text-lg text-gray-600">
            From simple HTML pages to complex documents, we've got you covered with specialized hosting solutions.
          </p>

          {/* Bento Grid Layout */}
          <div className="mt-10 grid gap-4 sm:mt-16 lg:grid-cols-3 lg:grid-rows-2">

            {/* HTML Hosting - Large Featured Card */}
            <div className="relative lg:row-span-2">
              <div className="absolute inset-px rounded-lg bg-white lg:rounded-l-[2rem]"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] lg:rounded-l-[calc(2rem+1px)]">
                <div className="px-8 pt-8 pb-3 sm:px-10 sm:pt-10 sm:pb-0">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-[#b249f8] to-[#9333ea] shadow-lg float-animation">
                      <HtmlIcon size={28} className="text-white" />
                    </div>
                    <div>
                      <p className="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">HTML Hosting</p>
                      <div className="flex flex-wrap items-center gap-2 mt-1">
                        <span className="px-2 py-1 bg-[#b249f8]/15 text-[#b249f8] text-xs rounded-full font-medium">Most Popular</span>
                        <span className="px-2 py-1 bg-emerald-500/15 text-emerald-600 text-xs rounded-full font-medium">Lightning Fast</span>
                      </div>
                    </div>
                  </div>
                  <p className="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
                    Deploy static websites, SPAs, and HTML applications with lightning speed. Perfect for portfolios, landing pages, and modern web applications.
                  </p>
                </div>
                <div className="relative min-h-[30rem] w-full grow max-lg:mx-auto max-lg:max-w-sm">
                  {/* Feature Grid */}
                  <div className="px-8 sm:px-10 mb-6">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                        <span className="text-sm text-gray-600">Global CDN</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                        <span className="text-sm text-gray-600">Auto SSL</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                        <span className="text-sm text-gray-600">Git Integration</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#b249f8] rounded-full"></div>
                        <span className="text-sm text-gray-600">Custom Domains</span>
                      </div>
                    </div>
                  </div>

                  {/* Mock Browser Window */}
                  <div className="absolute inset-x-8 top-20 bottom-20 overflow-hidden rounded-t-xl border-x-4 border-t-4 border-gray-300 bg-white shadow-2xl sm:inset-x-10">
                    <div className="flex items-center gap-2 bg-gray-100 px-4 py-2 border-b border-gray-200">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <div className="flex-1 bg-white rounded px-3 py-1 text-xs text-gray-600 ml-4">
                        https://your-site.statichost.com
                      </div>
                    </div>
                    <div className="p-4 space-y-3">
                      <div className="h-3 bg-gradient-to-r from-[#b249f8] to-purple-600 rounded-full w-3/4"></div>
                      <div className="h-2 bg-gray-200 rounded-full w-1/2"></div>
                      <div className="h-2 bg-gray-200 rounded-full w-2/3"></div>
                      <div className="h-2 bg-gray-200 rounded-full w-1/3"></div>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="absolute bottom-8 left-8 right-8 flex gap-3 sm:left-10 sm:right-10">
                    <Button
                      color="primary"
                      variant="shadow"
                      className="flex-1"
                      style={{ backgroundColor: "#b249f8" }}
                    >
                      Deploy Now
                    </Button>
                    <Button variant="bordered" className="flex-1 border-gray-300">
                      View Demo
                    </Button>
                  </div>
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow-sm ring-1 ring-black/5 lg:rounded-l-[2rem]"></div>
            </div>



            {/* PDF Hosting - Medium Card */}
            <div className="relative max-lg:row-start-1">
              <div className="absolute inset-px rounded-lg bg-white max-lg:rounded-t-[2rem]"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-t-[calc(2rem+1px)]">
                <div className="px-8 pt-8 sm:px-10 sm:pt-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-600 shadow-lg float-animation">
                      <PdfIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <p className="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">PDF Hosting</p>
                      <span className="px-2 py-1 bg-orange-500/15 text-orange-600 text-xs rounded-full font-medium">Secure</span>
                    </div>
                  </div>
                  <p className="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
                    Share documents, manuals, and reports with secure, fast PDF hosting. Built-in viewer and download protection.
                  </p>
                </div>
                <div className="flex flex-1 items-center justify-center px-8 max-lg:pt-10 max-lg:pb-12 sm:px-10 lg:pb-2">
                  {/* PDF Preview Mock */}
                  <div className="w-full max-w-sm bg-white rounded-lg shadow-lg border border-gray-200 p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-orange-500/15 rounded-lg">
                        <PdfIcon size={16} className="text-orange-600" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-700">annual-report.pdf</div>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-green-600 font-medium">Protected</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-400">2.4 MB</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-2 bg-gradient-to-r from-orange-400 to-red-400 rounded-full w-full"></div>
                      <div className="h-1.5 bg-gray-200 rounded-full w-4/5"></div>
                      <div className="h-1.5 bg-gray-200 rounded-full w-3/5"></div>
                    </div>
                    <div className="mt-4 space-y-2 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span className="text-gray-600">Password protection</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span className="text-gray-600">Download restrictions</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span className="text-gray-600">Analytics tracking</span>
                      </div>
                    </div>
                    <Button
                      color="warning"
                      variant="flat"
                      className="w-full mt-4"
                      size="sm"
                    >
                      Learn More
                    </Button>
                  </div>
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow-sm ring-1 ring-black/5 max-lg:rounded-t-[2rem]"></div>
            </div>

            {/* Resume Hosting - Medium Card */}
            <div className="relative max-lg:row-start-3 lg:col-start-2 lg:row-start-2">
              <div className="absolute inset-px rounded-lg bg-white"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
                <div className="px-8 pt-8 sm:px-10 sm:pt-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg float-animation">
                      <ResumeIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <p className="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Resume Hosting</p>
                      <span className="px-2 py-1 bg-green-500/15 text-green-600 text-xs rounded-full font-medium">Professional</span>
                    </div>
                  </div>
                  <p className="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
                    Professional resume hosting with custom URLs. Perfect for job applications and professional networking.
                  </p>
                </div>
                <div className="flex flex-1 items-center max-lg:py-6 lg:pb-2">
                  {/* Resume Preview Mock */}
                  <div className="w-full max-w-xs mx-auto bg-white rounded-lg shadow-lg border border-gray-200 p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">JD</span>
                      </div>
                      <div className="flex-1">
                        <div className="h-3 bg-gray-300 rounded w-20 mb-2"></div>
                        <div className="h-2 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="h-2 bg-gray-200 rounded-full w-full"></div>
                      <div className="h-2 bg-gray-200 rounded-full w-3/4"></div>
                      <div className="h-2 bg-gray-200 rounded-full w-1/2"></div>
                    </div>
                    <div className="space-y-2 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span className="text-gray-600">Custom domain</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span className="text-gray-600">SEO optimized</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span className="text-gray-600">View analytics</span>
                      </div>
                    </div>
                    <Button
                      color="success"
                      variant="flat"
                      className="w-full mt-4"
                      size="sm"
                    >
                      Get Started
                    </Button>
                  </div>
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow-sm ring-1 ring-black/5"></div>
            </div>

            {/* Advanced Features - Coming Soon */}
            <div className="relative lg:row-span-2">
              <div className="absolute inset-px rounded-lg bg-white max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)] max-lg:rounded-b-[calc(2rem+1px)] lg:rounded-r-[calc(2rem+1px)]">
                <div className="px-8 pt-8 pb-3 sm:px-10 sm:pt-10 sm:pb-0">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg float-animation">
                      <CloudIcon size={28} className="text-white" />
                    </div>
                    <div>
                      <p className="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Advanced Features</p>
                      <span className="px-2 py-1 bg-purple-500/15 text-purple-600 text-xs rounded-full font-medium">Coming Soon</span>
                    </div>
                  </div>
                  <p className="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
                    Database hosting, API endpoints, serverless functions, and more advanced features coming soon.
                  </p>
                </div>
                <div className="relative min-h-[30rem] w-full grow">
                  <div className="absolute top-10 right-0 bottom-0 left-10 overflow-hidden rounded-tl-xl bg-gray-900 shadow-2xl">
                    <div className="flex bg-gray-800/40 ring-1 ring-white/5">
                      <div className="-mb-px flex text-sm/6 font-medium text-gray-400">
                        <div className="border-r border-b border-r-white/10 border-b-white/20 bg-white/5 px-4 py-2 text-white">
                          api.js
                        </div>
                        <div className="border-r border-gray-600/10 px-4 py-2">database.js</div>
                      </div>
                    </div>
                    <div className="px-6 pt-6 pb-14">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <span className="text-purple-400 text-sm">export</span>
                          <span className="text-blue-400 text-sm">async function</span>
                          <span className="text-yellow-300 text-sm">handler</span>
                          <span className="text-gray-300 text-sm">(req, res) &#123;</span>
                        </div>
                        <div className="ml-4 space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-blue-400 text-sm">const</span>
                            <span className="text-white text-sm">data</span>
                            <span className="text-gray-300 text-sm">=</span>
                            <span className="text-blue-400 text-sm">await</span>
                            <span className="text-yellow-300 text-sm">db.query</span>
                            <span className="text-gray-300 text-sm">();</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-white text-sm">res</span>
                            <span className="text-gray-300 text-sm">.</span>
                            <span className="text-yellow-300 text-sm">json</span>
                            <span className="text-gray-300 text-sm">(data);</span>
                          </div>
                        </div>
                        <div className="text-gray-300 text-sm">&#125;</div>
                      </div>
                    </div>
                  </div>

                  {/* Feature list */}
                  <div className="absolute bottom-10 left-10 right-10">
                    <div className="grid grid-cols-2 gap-3 text-sm mb-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-600">Database hosting</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-600">API endpoints</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-600">Serverless functions</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span className="text-gray-600">Edge computing</span>
                        </div>
                      </div>
                    </div>
                    <Button
                      color="secondary"
                      variant="flat"
                      size="sm"
                      disabled
                      className="w-full"
                    >
                      Notify Me
                    </Button>
                  </div>
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow-sm ring-1 ring-black/5 max-lg:rounded-b-[2rem] lg:rounded-r-[2rem]"></div>
            </div>

            {/* Performance Stats Card */}
            <div className="relative max-lg:row-start-2">
              <div className="absolute inset-px rounded-lg bg-white"></div>
              <div className="relative flex h-full flex-col overflow-hidden rounded-[calc(theme(borderRadius.lg)+1px)]">
                <div className="px-8 pt-8 sm:px-10 sm:pt-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 shadow-lg">
                      <SpeedIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <p className="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Performance Stats</p>
                    </div>
                  </div>
                  <p className="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
                    Lightning-fast global performance with enterprise-grade reliability and monitoring.
                  </p>
                </div>
                <div className="flex flex-1 items-center justify-center px-8 max-lg:pt-10 max-lg:pb-12 sm:px-10 lg:pb-2">
                  <div className="w-full max-w-sm">
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-600 mb-1">99.9%</div>
                        <div className="text-xs text-gray-600">Uptime</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600 mb-1">&lt;50ms</div>
                        <div className="text-xs text-gray-600">Response</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-indigo-600 mb-1">200+</div>
                        <div className="text-xs text-gray-600">Locations</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600 mb-1">10M+</div>
                        <div className="text-xs text-gray-600">Sites</div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between text-xs mb-2">
                        <span className="text-gray-600">Global Performance</span>
                        <span className="text-green-600 font-medium">Excellent</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-cyan-500 to-blue-600 h-2 rounded-full w-[95%]"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="pointer-events-none absolute inset-px rounded-lg shadow-sm ring-1 ring-black/5"></div>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Features Section */}
      <section className="py-16 px-6 bg-content1">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className={title({ size: "md" })}>
              Why choose&nbsp;
              <span className={title({ color: "violet", size: "md" })}>
                StaticHost?
              </span>
            </h2>
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Built for speed, security, and simplicity. Everything you need to get your content online fast.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="p-4 rounded-full bg-primary/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <SpeedIcon size={32} className="text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Lightning Fast</h3>
              <p className="text-default-600 text-sm">
                Global CDN ensures your content loads instantly worldwide
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-success/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <SecurityIcon size={32} className="text-success" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Secure by Default</h3>
              <p className="text-default-600 text-sm">
                Free SSL certificates and DDoS protection included
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-secondary/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <GlobalIcon size={32} className="text-secondary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Global Reach</h3>
              <p className="text-default-600 text-sm">
                Deployed across 200+ edge locations worldwide
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-warning/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CloudIcon size={32} className="text-warning" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Zero Config</h3>
              <p className="text-default-600 text-sm">
                Deploy with a simple drag & drop or Git integration
              </p>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* CTA Section */}
      <section className="py-16 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="p-12 bg-gradient-to-br from-primary/10 to-secondary/10 border-none">
            <CardBody>
              <h2 className={title({ size: "md" })}>
                Ready to deploy your&nbsp;
                <span className={title({ color: "violet", size: "md" })}>
                  next project?
                </span>
              </h2>
              <p className={subtitle({ class: "mt-4 mb-8 max-w-2xl mx-auto" })}>
                Join thousands of developers who trust StaticHost for their hosting needs.
                Start with our free tier and scale as you grow.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  color="primary"
                  variant="shadow"
                  className="font-semibold px-8 py-6 text-lg"
                  style={{ backgroundColor: "#b249f8" }}
                >
                  <RocketIcon size={20} />
                  Start Free Trial
                </Button>
                <Button
                  size="lg"
                  variant="bordered"
                  className="font-semibold px-8 py-6 text-lg"
                  as={Link}
                  href="/pricing"
                >
                  View Pricing
                </Button>
              </div>

              <div className="mt-8 flex flex-wrap justify-center gap-8 text-sm text-default-500">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  No credit card required
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  Free SSL & CDN
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  24/7 Support
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>

      <Spacer y={8} />

      {/* Stats Section */}
      <section className="py-16 px-6 bg-content1">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">99.9%</div>
              <div className="text-default-600">Uptime SLA</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">200+</div>
              <div className="text-default-600">Edge Locations</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">10M+</div>
              <div className="text-default-600">Sites Hosted</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">&lt;100ms</div>
              <div className="text-default-600">Average Response</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
