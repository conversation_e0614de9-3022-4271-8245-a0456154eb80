import { Link } from "@heroui/link";
import { <PERSON><PERSON> } from "@heroui/button";
import { <PERSON>, <PERSON><PERSON>eader, CardB<PERSON>, CardFooter } from "@heroui/card";
import { Spacer } from "@heroui/spacer";
import { Image } from "@heroui/image";

import { siteConfig } from "@/config/site";
import { title, subtitle } from "@/components/primitives";
import {
  HtmlIcon,
  PdfIcon,
  ResumeIcon,
  CloudIcon,
  SpeedIcon,
  SecurityIcon,
  GlobalIcon,
  RocketIcon
} from "@/components/icons";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 py-16 md:py-24 text-center">
        <div className="max-w-4xl mx-auto px-6">
          <h1 className={title({ size: "lg" })}>
            Lightning-fast&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              static hosting&nbsp;
            </span>
            <br />
            for modern web applications
          </h1>
          <div className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
            Deploy HTML sites, PDFs, resumes, and documents instantly with zero configuration.
            Global CDN, SSL certificates, and custom domains included.
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <Button
            size="lg"
            color="primary"
            variant="shadow"
            className="font-semibold px-8 py-6 text-lg"
            style={{ backgroundColor: "#b249f8" }}
          >
            <RocketIcon size={20} />
            Deploy Now - Free
          </Button>
          <Button
            size="lg"
            variant="bordered"
            className="font-semibold px-8 py-6 text-lg"
          >
            View Documentation
          </Button>
        </div>

        <div className="mt-12 text-sm text-default-500">
          ⚡ Deploy in seconds • 🌍 Global CDN • 🔒 Free SSL • 📱 Mobile optimized
        </div>
      </section>

      <Spacer y={8} />

      {/* Services Section - Bento Grid */}
      <section className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className={title({ size: "md" })}>
              Perfect hosting for&nbsp;
              <span className={title({ color: "violet", size: "md" })}>
                every project
              </span>
            </h2>
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              From simple HTML pages to complex documents, we've got you covered with specialized hosting solutions.
            </p>
          </div>

          {/* Bento Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 auto-rows-fr">

            {/* HTML Hosting - Large Featured Card */}
            <Card className="lg:col-span-7 lg:row-span-2 p-0 overflow-hidden group hover:scale-[1.01] transition-all duration-500 bg-gradient-to-br from-default-50 to-default-100 border border-default-200 hover:border-primary/30 card-hover-effect">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                <div className="w-32 h-32 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-2xl"></div>
              </div>
              <CardBody className="p-8 relative z-10 flex flex-col h-full">
                <div className="flex items-start gap-4 mb-6">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary to-secondary shadow-lg shadow-primary/25 float-animation">
                    <HtmlIcon size={32} className="text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold mb-2 text-foreground">HTML Hosting</h3>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full font-medium border border-primary/20">Most Popular</span>
                      <span className="px-3 py-1 bg-success/10 text-success text-xs rounded-full font-medium border border-success/20">Lightning Fast</span>
                    </div>
                    <p className="text-default-600 text-base leading-relaxed">
                      Deploy static websites, SPAs, and HTML applications with lightning speed.
                      Perfect for portfolios, landing pages, and modern web applications.
                    </p>
                  </div>
                </div>

                {/* Feature Grid */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm text-default-700">Global CDN</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm text-default-700">Auto SSL</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm text-default-700">Git Integration</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm text-default-700">Custom Domains</span>
                  </div>
                </div>

                {/* Mock Browser Window */}
                <div className="bg-white/50 dark:bg-default-100/50 rounded-xl p-4 border border-default-200/50 backdrop-blur-sm mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <div className="flex-1 bg-default-200/50 rounded-md px-3 py-1 text-xs text-default-600">
                      https://your-site.statichost.com
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-2 bg-gradient-to-r from-primary to-secondary rounded-full w-3/4"></div>
                    <div className="h-2 bg-default-300/60 rounded-full w-1/2"></div>
                    <div className="h-2 bg-default-300/60 rounded-full w-2/3"></div>
                  </div>
                </div>

                <div className="flex gap-3 mt-auto">
                  <Button
                    color="primary"
                    variant="shadow"
                    className="flex-1"
                    style={{ backgroundColor: "#b249f8" }}
                  >
                    Deploy Now
                  </Button>
                  <Button variant="bordered" className="flex-1 border-default-300">
                    View Demo
                  </Button>
                </div>
              </CardBody>
            </Card>

            {/* PDF Hosting - Medium Card */}
            <Card className="lg:col-span-5 p-0 overflow-hidden group hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-orange-500/10 via-red-500/10 to-pink-500/10 border border-orange-500/20 card-hover-effect">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-600/5 via-red-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-2 right-2 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-lg"></div>
              </div>
              <CardBody className="p-6 h-full flex flex-col justify-between relative z-10">
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-600 shadow-lg float-animation">
                      <PdfIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">PDF Hosting</h3>
                      <span className="px-2 py-1 bg-warning/20 text-warning text-xs rounded-full font-medium">Secure</span>
                    </div>
                  </div>

                  <p className="text-default-600 mb-4 text-sm leading-relaxed">
                    Share documents, manuals, and reports with secure, fast PDF hosting.
                    Built-in viewer and download protection.
                  </p>

                  {/* PDF Preview Mock */}
                  <div className="bg-default-100 rounded-lg p-3 border border-default-200 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <PdfIcon size={16} className="text-orange-500" />
                      <span className="text-xs text-default-600">document.pdf</span>
                      <span className="text-xs text-success">Protected</span>
                    </div>
                    <div className="space-y-1">
                      <div className="h-1.5 bg-default-300 rounded w-full"></div>
                      <div className="h-1.5 bg-default-300 rounded w-4/5"></div>
                      <div className="h-1.5 bg-default-300 rounded w-3/5"></div>
                    </div>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                      <span className="text-default-700">Password protection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                      <span className="text-default-700">Download restrictions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                      <span className="text-default-700">Analytics tracking</span>
                    </div>
                  </div>
                </div>

                <Button
                  color="warning"
                  variant="flat"
                  className="w-full mt-4"
                >
                  Learn More
                </Button>
              </CardBody>
            </Card>

            {/* Resume Hosting - Medium Card */}
            <Card className="lg:col-span-5 p-0 overflow-hidden group hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 border border-green-500/20 card-hover-effect">
              <div className="absolute inset-0 bg-gradient-to-br from-green-600/5 via-emerald-600/5 to-teal-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-2 right-2 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full blur-lg"></div>
              </div>
              <CardBody className="p-6 h-full flex flex-col justify-between relative z-10">
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg float-animation">
                      <ResumeIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Resume Hosting</h3>
                      <span className="px-2 py-1 bg-success/20 text-success text-xs rounded-full font-medium">Professional</span>
                    </div>
                  </div>

                  <p className="text-default-600 mb-4 text-sm leading-relaxed">
                    Professional resume hosting with custom URLs. Perfect for job applications
                    and professional networking.
                  </p>

                  {/* Resume Preview Mock */}
                  <div className="bg-default-100 rounded-lg p-3 border border-default-200 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">JD</span>
                      </div>
                      <div>
                        <div className="h-2 bg-default-400 rounded w-16 mb-1"></div>
                        <div className="h-1 bg-default-300 rounded w-12"></div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-1 bg-default-300 rounded w-full"></div>
                      <div className="h-1 bg-default-300 rounded w-3/4"></div>
                      <div className="h-1 bg-default-300 rounded w-1/2"></div>
                    </div>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span className="text-default-700">Custom domain</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span className="text-default-700">SEO optimized</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span className="text-default-700">View analytics</span>
                    </div>
                  </div>
                </div>

                <Button
                  color="success"
                  variant="flat"
                  className="w-full mt-4"
                >
                  Get Started
                </Button>
              </CardBody>
            </Card>

            {/* Additional Services - Coming Soon */}
            <Card className="lg:col-span-7 p-0 overflow-hidden group hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-rose-500/10 border border-purple-500/20 card-hover-effect">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-pink-600/5 to-rose-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <div className="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-lg"></div>
              </div>
              <CardBody className="p-6 h-full flex items-center justify-between relative z-10">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg">
                      <CloudIcon size={24} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Advanced Features</h3>
                      <span className="px-2 py-1 bg-purple-500/20 text-purple-600 text-xs rounded-full font-medium">Coming Soon</span>
                    </div>
                  </div>

                  <p className="text-default-600 mb-4 text-sm leading-relaxed">
                    Database hosting, API endpoints, serverless functions, and more advanced features coming soon.
                  </p>

                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span className="text-default-700">Database hosting</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span className="text-default-700">API endpoints</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span className="text-default-700">Serverless functions</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span className="text-default-700">Edge computing</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="ml-6 flex flex-col items-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center mb-3">
                    <RocketIcon size={32} className="text-purple-500" />
                  </div>
                  <Button
                    color="secondary"
                    variant="flat"
                    size="sm"
                    disabled
                  >
                    Notify Me
                  </Button>
                </div>
              </CardBody>
            </Card>

            {/* Quick Stats Card */}
            <Card className="lg:col-span-5 p-0 overflow-hidden group hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-cyan-500/10 via-blue-500/10 to-indigo-500/10 border border-cyan-500/20 card-hover-effect">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-600/5 via-blue-600/5 to-indigo-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-2 right-2 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-full blur-lg"></div>
              </div>
              <CardBody className="p-6 h-full flex flex-col justify-center relative z-10">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 shadow-lg">
                      <SpeedIcon size={20} className="text-white" />
                    </div>
                    <h3 className="text-lg font-bold">Performance Stats</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-cyan-600 mb-1">99.9%</div>
                      <div className="text-xs text-default-600">Uptime</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600 mb-1">&lt;50ms</div>
                      <div className="text-xs text-default-600">Response</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-indigo-600 mb-1">200+</div>
                      <div className="text-xs text-default-600">Locations</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600 mb-1">10M+</div>
                      <div className="text-xs text-default-600">Sites</div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-default-100 rounded-lg">
                    <div className="flex items-center justify-between text-xs mb-2">
                      <span className="text-default-600">Global Performance</span>
                      <span className="text-success font-medium">Excellent</span>
                    </div>
                    <div className="w-full bg-default-200 rounded-full h-2">
                      <div className="bg-gradient-to-r from-cyan-500 to-blue-600 h-2 rounded-full w-[95%]"></div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* Features Section */}
      <section className="py-16 px-6 bg-content1">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className={title({ size: "md" })}>
              Why choose&nbsp;
              <span className={title({ color: "violet", size: "md" })}>
                StaticHost?
              </span>
            </h2>
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Built for speed, security, and simplicity. Everything you need to get your content online fast.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="p-4 rounded-full bg-primary/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <SpeedIcon size={32} className="text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Lightning Fast</h3>
              <p className="text-default-600 text-sm">
                Global CDN ensures your content loads instantly worldwide
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-success/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <SecurityIcon size={32} className="text-success" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Secure by Default</h3>
              <p className="text-default-600 text-sm">
                Free SSL certificates and DDoS protection included
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-secondary/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <GlobalIcon size={32} className="text-secondary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Global Reach</h3>
              <p className="text-default-600 text-sm">
                Deployed across 200+ edge locations worldwide
              </p>
            </div>

            <div className="text-center">
              <div className="p-4 rounded-full bg-warning/10 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CloudIcon size={32} className="text-warning" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Zero Config</h3>
              <p className="text-default-600 text-sm">
                Deploy with a simple drag & drop or Git integration
              </p>
            </div>
          </div>
        </div>
      </section>

      <Spacer y={8} />

      {/* CTA Section */}
      <section className="py-16 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="p-12 bg-gradient-to-br from-primary/10 to-secondary/10 border-none">
            <CardBody>
              <h2 className={title({ size: "md" })}>
                Ready to deploy your&nbsp;
                <span className={title({ color: "violet", size: "md" })}>
                  next project?
                </span>
              </h2>
              <p className={subtitle({ class: "mt-4 mb-8 max-w-2xl mx-auto" })}>
                Join thousands of developers who trust StaticHost for their hosting needs.
                Start with our free tier and scale as you grow.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  color="primary"
                  variant="shadow"
                  className="font-semibold px-8 py-6 text-lg"
                  style={{ backgroundColor: "#b249f8" }}
                >
                  <RocketIcon size={20} />
                  Start Free Trial
                </Button>
                <Button
                  size="lg"
                  variant="bordered"
                  className="font-semibold px-8 py-6 text-lg"
                  as={Link}
                  href="/pricing"
                >
                  View Pricing
                </Button>
              </div>

              <div className="mt-8 flex flex-wrap justify-center gap-8 text-sm text-default-500">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  No credit card required
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  Free SSL & CDN
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-success rounded-full"></span>
                  24/7 Support
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </section>

      <Spacer y={8} />

      {/* Stats Section */}
      <section className="py-16 px-6 bg-content1">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">99.9%</div>
              <div className="text-default-600">Uptime SLA</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">200+</div>
              <div className="text-default-600">Edge Locations</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">10M+</div>
              <div className="text-default-600">Sites Hosted</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">&lt;100ms</div>
              <div className="text-default-600">Average Response</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
