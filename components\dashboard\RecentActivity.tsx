"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { 
  RocketIcon, 
  GitBranchIcon, 
  GlobeIcon,
  SettingsIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from "@/components/icons";

interface ActivityItem {
  id: string;
  type: "deployment" | "domain" | "settings" | "git";
  title: string;
  description: string;
  timestamp: string;
  status: "success" | "error" | "pending";
  project?: string;
}

interface ActivityItemProps {
  activity: ActivityItem;
}

const ActivityItemComponent: React.FC<ActivityItemProps> = ({ activity }) => {
  const getIcon = () => {
    switch (activity.type) {
      case "deployment":
        return <RocketIcon size={16} className="text-orange-500" />;
      case "domain":
        return <GlobeIcon size={16} className="text-blue-400" />;
      case "settings":
        return <SettingsIcon size={16} className="text-gray-400" />;
      case "git":
        return <GitBranchIcon size={16} className="text-purple-400" />;
      default:
        return <ClockIcon size={16} className="text-gray-400" />;
    }
  };

  const getStatusIcon = () => {
    switch (activity.status) {
      case "success":
        return <CheckCircleIcon size={14} className="text-green-400" />;
      case "error":
        return <XCircleIcon size={14} className="text-red-400" />;
      case "pending":
        return <ClockIcon size={14} className="text-yellow-400" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (activity.status) {
      case "success":
        return "success";
      case "error":
        return "danger";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  return (
    <div className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-800/30 transition-colors">
      <div className="p-2 rounded-lg bg-gray-800/50 border border-gray-700/50">
        {getIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-2 mb-1">
          <h4 className="text-white font-medium text-sm truncate">{activity.title}</h4>
          <div className="flex items-center gap-1">
            {getStatusIcon()}
          </div>
        </div>
        
        <p className="text-gray-400 text-xs leading-relaxed mb-2">
          {activity.description}
        </p>
        
        <div className="flex items-center justify-between">
          {activity.project && (
            <Chip size="sm" variant="flat" className="bg-gray-800/50 text-gray-300">
              {activity.project}
            </Chip>
          )}
          <span className="text-gray-500 text-xs">{activity.timestamp}</span>
        </div>
      </div>
    </div>
  );
};

interface RecentActivityProps {
  className?: string;
}

export const RecentActivity: React.FC<RecentActivityProps> = ({ className }) => {
  const activities: ActivityItem[] = [
    {
      id: "1",
      type: "deployment",
      title: "Deployment successful",
      description: "Portfolio Website deployed to production",
      timestamp: "2 hours ago",
      status: "success",
      project: "Portfolio"
    },
    {
      id: "2",
      type: "git",
      title: "Repository connected",
      description: "Connected GitHub repository for auto-deployments",
      timestamp: "4 hours ago",
      status: "success",
      project: "Company Landing"
    },
    {
      id: "3",
      type: "domain",
      title: "Custom domain added",
      description: "Added acme-corp.com with SSL certificate",
      timestamp: "1 day ago",
      status: "success",
      project: "Company Landing"
    },
    {
      id: "4",
      type: "deployment",
      title: "Build in progress",
      description: "Documentation Site is being built and deployed",
      timestamp: "1 day ago",
      status: "pending",
      project: "Docs Site"
    },
    {
      id: "5",
      type: "settings",
      title: "Environment variables updated",
      description: "Updated API keys and configuration settings",
      timestamp: "2 days ago",
      status: "success",
      project: "Blog"
    },
    {
      id: "6",
      type: "deployment",
      title: "Deployment failed",
      description: "Build failed due to missing dependencies",
      timestamp: "3 days ago",
      status: "error",
      project: "Docs Site"
    }
  ];

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div>
              <h2 className="text-xl font-bold text-white">Recent Activity</h2>
              <p className="text-gray-400 text-sm">Latest updates and deployments</p>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {activities.map((activity) => (
              <ActivityItemComponent key={activity.id} activity={activity} />
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-700/50">
            <button className="text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors">
              View all activity →
            </button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
